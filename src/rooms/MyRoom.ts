import { Room, Client } from "@colyseus/core";
import {
  GameState,
  PlayerState,
  Player,
  UpdateData,
  RoomMetadataSchema,
} from "./schema/MyRoomState";
import { ServerError } from "colyseus";
import { GetUserInfo } from "./config/auth";
import { GetMonsterData } from "./config/GetMonsterData";
import { postBattleData, RankedMatchHistory } from "./config/StoreMatchHistory";
import { SyncTurnData } from "./BattleManager";
import {GetFarmData} from "./config/GetFarmData";

const LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

export class MyRoom extends Room<GameState> {
  maxClients = 10;
  password = "";
  hostCLID = "";
  LOBBY_CHANNEL = "free_battle";
  roomType: MatchType = 0;

  async onAuth(
    client: Client,
    options: any,
    request: import("http").IncomingMessage
  ) {
    // console.log(options.token);
    var response = await GetUserInfo(options.token);
    if (!response.success) {
      throw new ServerError(400, "bad access token");
    } else {
      // console.log(response);
      return true;
    }
  }

  async generateRoomId(): Promise<string> {
    const currentIds = await this.presence.smembers(this.LOBBY_CHANNEL);
    let id;
    do {
      id = this.generateRoomIdSingle();
    } while (currentIds.includes(id));

    await this.presence.sadd(this.LOBBY_CHANNEL, id);
    return id;
  }

  generateRoomIdSingle(): string {
    let result = "";
    for (var i = 0; i < 4; i++) {
      result += LETTERS.charAt(Math.floor(Math.random() * LETTERS.length));
    }
    return result;
  }

  updateStateFromMetadata(metadata: any): void {
    this.state.metadata.firstPlayer = metadata.firstPlayer || "";
    this.state.metadata.secondPlayer = metadata.secondPlayer || "";
    this.state.metadata.audience = metadata.audience || false;
    this.state.metadata.opponent = metadata.opponent || false;
    this.state.metadata.monsterRank = metadata.monsterRank;
    this.state.metadata.monsterNum = metadata.monsterNum;
    this.state.metadata.turnNum = metadata.turnNum;
    this.state.metadata.specialHex = metadata.specialHex || "None";
    this.state.metadata.memoryAllow = metadata.memoryAllow || false;
    this.state.metadata.hostId = metadata.hostId || "defaultHostId";
  }
  // Handle countdown finished
  onCountdownFinished() {
    // console.log("Countdown finished!");
    this.broadcast("countdown_finished", "The countdown is over!");
  }

  async onCreate(options: any) {
    this.roomType = this.convertRoomNameToMatchType(this.roomName);

    if (this.roomType === MatchType.Ranked) {
      this.maxClients = 2;
      this.LOBBY_CHANNEL = this.roomName;
    }
    
    this.roomId = this.roomName === "tournament_match" ? options.roomName : await this.generateRoomId();

    this.autoDispose = true;
    this.setMetadata({
      ["isInBattle"]: false,
      ["firstPlayer"]: options.metadata.firstPlayer,
      ["secondPlayer"]: options.metadata.secondPlayer,
      ["roomName"]: options.roomName,
    });
    this.setMetadata(options.metadata);
    var newState = new GameState();
    this.setState(newState);
    this.state.random = getRandomFloat(0.0, 100.0);

    if (options.roomName !== undefined) {
      this.state.roomName = options.roomName;
    } else {
      this.state.roomName = "";
    }
    this.updateStateFromMetadata(options.metadata);
    // console.log(options);
    this.setPrivate(options.private);
    this.state.metadata.isPrivate = options.private;

    if (options.password) {
      this.state.metadata.password = options.password;
      this.password = options.password;
    }

    this.onMessage("player_ready", (client, message) => {
      const player = this.state.players.find((p) => p.id === message.content);
      if (player) {
        player.state =
          player.state === PlayerState.ReadyToStart
            ? PlayerState.NotReady
            : PlayerState.ReadyToStart;
      }
      if (this.state.players.length === 2) {
        if (
          this.state.players.every((p) => p.state === PlayerState.ReadyToStart)
        ) {
          this.state.countdown = 60;
          let countdownInterval = this.clock.setInterval(() => {
            if (this.state.countdown > 0) {
              this.state.countdown--;
              this.broadcast("countdown", this.state.countdown);
            } else {
              this.onCountdownFinished();
              this.clock.clear();
            }
          }, 1000);
        }
      }
    });

    this.onMessage("player_watch", (client, message) => {
      if (
        client.id != this.state.players[0].sessionId &&
        client.id != this.state.players[1].sessionId &&
        !this.state.spectatorList.includes(client.sessionId)
      ) {
        this.state.spectatorList.push(client.sessionId);
        this.state.metadata.spectatorCount = this.state.spectatorList.length;
      }
    });

    this.onMessage("player_enter", (client, message) => {
      if (this.state.players.length < 2) {
        const newPlayer = new Player(
          message.playerId,
          client.sessionId,
          message.walletId
        );
        newPlayer.state = PlayerState.NotReady;
        this.state.players.push(newPlayer);
        if (this.state.players.length === 2) {
          this.state.metadata.secondPlayer = message.playerName;
          this.setMetadata({ ["secondPlayer"]: message.playerName });
          this.state.playerRoles.secondPlayerId = message.playerId;
        }
      }
    });

    // this.onMessage("tournament_setting", (client, message: BattleStart) => {
    //   this.state.metadata.monsterRank = message.monsterRank;
    //   this.state.metadata.monsterNum = message.monsterNum;
    //   this.state.metadata.turnNum = message.turnNum;
    //   this.state.metadata.specialHex = message.specialHex == "Random" ? this.randomSpecialHex() : message.specialHex;
    //   this.state.metadata.memoryAllow = message.isAllowMemory;
    // });

    this.onMessage("kick_player", (client) => {
      if (
        client.sessionId === this.state.players[0].sessionId &&
        this.state.players.length === 2
      ) {
        const client = this.clients.find(
          (client) => client.sessionId === this.state.players[1].sessionId
        );
        if (client) {
          client.leave(); // Gracefully disconnects the client
        }
        this.setMetadata({ ["secondPlayer"]: "" });
        this.state.players.pop();
        this.state.playerRoles.secondPlayerId = "";
        this.state.metadata.secondPlayer = "";
      }
    });

    this.onMessage("setting_update", (client, message) => {
      const { field, value } = message;

      switch (field) {
        case "monsterRank":
          this.state.metadata.monsterRank = value;
          break;
        case "monsterNum":
          this.state.metadata.monsterNum = value;
          break;
        case "turnNum":
          this.state.metadata.turnNum = value;
          break;
        case "specialHex":
          this.state.metadata.specialHex = value;
          break;
        case "isAllowMemory":
          this.state.metadata.memoryAllow = value === "Allowed";
          break;
        default:
          console.log(`Unknown field: ${field}`);
          break;
      }
    });

    this.onMessage("player_sur", (client) => {
      this.broadcast("conmemay", "Your Opponent Surrendered The Match", {
        except: client,
      });
      this.broadcast("opponent_sur", "Your Opponent Left The Match", {
        except: client,
      });
      this.clock.clear();
    });

    this.onMessage("party_ready", (client, message) => {
      const player = this.state.players.find((p) => p.id === message.content);
      if (player) {
        player.state =
          player.state === PlayerState.ReadyInPartySetting
            ? PlayerState.ReadyToStart
            : PlayerState.ReadyInPartySetting;
      }
      if (this.state.players.length === 2) {
        if (
          this.state.players.every(
            (p) => p.state === PlayerState.ReadyInPartySetting
          )
        ) {
          this.broadcast("both_ready", "siu");
          if (this.state.metadata.specialHex === "Random") {
            this.state.metadata.specialHex = this.randomSpecialHex();
          }
          this.clock.clear();
        }
      }
    });

    this.onMessage("add_monster", async (client, message) => {
      try {
        // Fetch both monster data and farm data concurrently
        const [monsterListData, farmListData] = await Promise.all([
          this.getPlayerBalanceMonster(message.token),
          this.getPlayerFarmData(message.token)
        ]);

        const monsterList = monsterListData.data.player_monster_list;
        const farmList = farmListData.data.player_farm_list;

        const foundMonster = monsterList.find(
          (monster) => monster._id === message.monsterId
        );

        const foundFarm = farmList.find(
          (farm) => farm._id === message.farmId
        );

        if (foundMonster) {
          this.state.addMonster(
            message.playerId,
            message.farmId,
            getRandomFloat(
              0.95 - message.randomNumberCorrection,
              1.05 + message.randomNumberCorrection
            ),
            JSON.stringify(foundMonster),
            foundFarm ? JSON.stringify(foundFarm) : ""
          );
        }
      } catch (error) {
        console.error("Error in add_monster handler:", error);
      }
    });

    this.onMessage("place_monster", (client, message: PlaceMonster[]) => {
      if (message.length > 0) {
        for (var i = 0; i < message.length; i++) {
          const monsterId = message[i].monsterId;
          for (let monster of this.state.monsters) {
            if (monster.monsterId === monsterId) {
              if (message[i].position) {
                monster.position.x = message[i].position.x;
                monster.position.y = message[i].position.y;
              }
            }
          }
        }

        const player = this.state.players.find(
          (p) => p.id == message[0].playerId
        );
        if (player) {
          player.state =
            player.state === PlayerState.ReadyToPlay
              ? PlayerState.ReadyToPlaceMonster
              : PlayerState.ReadyToPlay;
          if (this.state.players.length === 2) {
            if (
              this.state.players.every(
                (p) => p.state === PlayerState.ReadyToPlay
              )
            ) {
              this.gameReady();
            }
          }
        }
      } else {
        client.send("player_sur");
      }
    });

    this.onMessage("update_turn", (client, message: number) => {
      this.state.turnCount = message;
    });

    this.onMessage("monster_defense", (client, message: string) => {
      this.broadcast("monster_defense", message, {
        except: client,
      });
    });

    this.onMessage("monster_endTurn", (client, message: string) => {
      // Order By Descending this.state.monsters by monsters.currentSpeed - monsters.skillDelay
      this.state.monsters.sort((a, b) => {
        const aSpeed = a.currentSpeed - a.skillDelay;
        const bSpeed = b.currentSpeed - b.skillDelay;
        return bSpeed - aSpeed;
      });

      this.broadcast("monster_endTurn", message, {
        except: client,
      });
      this.state.random = getRandomFloat(0.0, 100.0);
      this.turnCountdown(false);
    });

    this.onMessage("monster_move", (client, message: MoveMessage) => {
      this.broadcast("monster_move", message, {
        except: client,
      });
    });

    this.onMessage("monster_ability", (client, message: PlayerAction) => {
      this.broadcast("monster_ability", message, {
        except: client,
      });
      this.turnCountdown(false);
    });

    this.onMessage("requestNewClock", (client, message) => {
      this.turnCountdown(false);
    });

    this.onMessage("player_loaded", (client, message: string) => {
      const player = this.state.players.find((p) => p.id === message);
      if (player) {
        player.state =
          player.state === PlayerState.ReadyToPlaceMonster
            ? PlayerState.ReadyInPartySetting
            : PlayerState.ReadyToPlaceMonster;
        if (this.state.players.length === 2) {
          if (
            this.state.players.every(
              (p) => p.state === PlayerState.ReadyToPlaceMonster
            )
          ) {
            this.state.isInBattle = true;
            this.setMetadata({ ["isInBattle"]: true });
            this.broadcast("placement_ready");
            this.turnCountdown(true);
          }
        }
      }
    });

    this.onMessage("game_end", async (client, message) => {
      console.log("Game Ended " + message);
      this.state.players.forEach((player) => {
        player.state = PlayerState.NotReady;
      });

      // only store when it is not a free battle
      if (this.roomType != MatchType.Free) {
        if (this.gameEndHandled) return;

        // Check if we have exactly 2 players before accessing their data
        if (this.state.players.length < 2) {
          console.warn("Game ended but less than 2 players present. Skipping battle data storage.");
          return;
        }

        // Additional safety checks for player data
        if (!this.state.players[0] || !this.state.players[1]) {
          console.warn("Game ended but player data is missing. Skipping battle data storage.");
          return;
        }

        if (!this.state.players[0].walletId || !this.state.players[1].walletId) {
          console.warn("Game ended but player wallet IDs are missing. Skipping battle data storage.");
          return;
        }

        const battleData: RankedMatchHistory = {
          match_time: Math.floor(Date.now() / 1000), // Current Unix timestamp
          user_wallet_a: this.state.players[0].walletId, // Wallet ID for Player A
          user_wallet_a_overall: message.overallA, // Overall score for Player A
          user_a_monsters: this.getMonsterIds(this.state.players[0].id), // Monsters for Player A
          user_a_score: message.scoreA, // Score for Player A
          user_a_streak: 0, // Streak for Player A (set to 0 as per original code)
          user_wallet_b: this.state.players[1].walletId, // Wallet ID for Player B
          user_wallet_b_overall: message.overallB, // Overall score for Player B
          user_b_monsters: this.getMonsterIds(this.state.players[1].id), // Monsters for Player B
          user_b_score: message.scoreB, // Score for Player B
          user_b_streak: 0, // Streak for Player B (set to 0 as per original code)
          battle_type: Number(this.convertRoomNameToMatchType(this.roomName)), // Battle type converted to number
          battle_rank: this.state.metadata.monsterRank, // Battle rank
          winner_user: message.winnerUser, // Winner's wallet ID
        };

        postBattleData(options.token, battleData)
          .then((result) => {
            // console.log("Battle data posted successfully:", result);
            this.gameEndHandled = true;
          })
          .catch((error) => {
            console.error("Error posting battle data:", error);
          });
      }

      if (this.roomType == MatchType.Free) {
        this.resetGameState();
      }
    });

    this.onMessage("sync_turnData", (client, message) => {
      SyncTurnData(this.state, message);
    });
  }
  gameEndHandled: boolean = false;
  resetGameState() {
    this.state.countdown = 60;
    this.state.isInBattle = false;
    this.setMetadata({ ["isInBattle"]: false });
    this.state.monsters.splice(0, this.state.monsters.length);
    this.state.currentMonsterTurn.splice(
      0,
      this.state.currentMonsterTurn.length
    );
    this.state.nextMonsterTurn.splice(0, this.state.nextMonsterTurn.length);
    this.state.currentSelectedMonster = "";
    this.state.turnCount = 0;
  }
  turnCountDown: number = 18;
  gameReady() {
    this.broadcast("game_ready", "Game is ready to start");
    this.turnCountdown(false);
  }

  turnCountdown(isPlacement: boolean) {
    let maxValue = 60;
    if (!isPlacement) {
      maxValue = 18;
    }
    this.turnCountDown = maxValue;
    if (this.clock) this.clock.clear();
    let countdownInterval = this.clock.setInterval(() => {
      if (this.turnCountDown > 0) {
        this.turnCountDown--;
        this.broadcast("turnCountDown", this.turnCountDown);
      } else {
        if (!isPlacement) {
          this.broadcast("forceEndTurn", "");
        } else {
          this.broadcast("forcePlaceMonster", "");
        }
        this.clock.clear();
      }
    }, 1000);
  }

  // search a json for a stri
  async getPlayerBalanceMonster(token: string) {
    return await GetMonsterData(token);
  }

  async getPlayerFarmData(token: string) {
    return await GetFarmData(token);
  }

  onJoin(client: Client, options: any) {
    // Handle host Player (1st Player)
    const newPlayer = new Player(
      options.playerId,
      client.sessionId,
      options.walletId
    );
    if (this.state.players.length === 0) {
      newPlayer.state = PlayerState.NotReady;
      this.state.players.push(newPlayer);
      this.state.metadata.firstPlayer = options.playerName;
      this.setMetadata({ ["firstPlayer"]: options.playerName });
      this.hostCLID = client.id;
      this.state.playerRoles.firstPlayerId = options.playerId;
    } else {
      if (
        this.roomName === "ranked_battle" ||
        this.roomName === "prized_battle"
      ) {
        newPlayer.state = PlayerState.NotReady;
        this.state.players.push(newPlayer);
        this.state.metadata.secondPlayer = options.playerName;
        this.setMetadata({ ["secondPlayer"]: options.playerName });
        this.state.playerRoles.secondPlayerId = options.playerId;

        // change all state to ReadyToStart
        this.state.players.forEach((player) => {
          player.state = PlayerState.ReadyToStart;
        });
        this.broadcast("start_partysetting", "");
        this.state.countdown = 60;
        this.clock.setInterval(() => {
          if (this.state.countdown > 0) {
            this.state.countdown--;
            this.broadcast("countdown", this.state.countdown);
          } else {
            this.onCountdownFinished();
            this.clock.clear();
          }
        }, 1000);
      }
    }
    if (this.state.players.length == 2) {
      const player = this.state.players.find((p) => p.id === newPlayer.id);
      if (player) {
        if (player.sessionId !== client.sessionId) {
          player.sessionId = client.sessionId;
          player.state = PlayerState.ReadyToPlay;
          this.broadcast("opponent_reconnect", player.id);
        }
      }
    }

    if (!options.password && this.password) {
      throw new ServerError(369, "Enter password field");
    }
    if (options.password !== this.password && this.password) {
      throw new ServerError(400, "Invalid password");
    }
  }
  findClientBySessionId(sessionId: string): Client | undefined {
    return Array.from(this.clients.values()).find(
      (client) => client.sessionId === sessionId
    );
  }

  async onLeave(client: Client, consented: boolean) {
    // try {
    // update this to remove player from the game ( rmeove from the state)
    const player = this.state.players.find(
      (p) => p.sessionId === client.sessionId
    );
    if (player) {
      if (player.state != PlayerState.ReadyToPlay) {
        this.state.players.splice(this.state.players.indexOf(player), 1);
        this.state.metadata.secondPlayer = "";
        this.setMetadata({ ["secondPlayer"]: "" });
        this.state.playerRoles.secondPlayerId = "";
      } else {
        player.state = PlayerState.Disconnected;
      }
    } else {
      const spectator = this.state.spectatorList.findIndex(
        (s) => s === client.sessionId
      );
      this.state.spectatorList.splice(spectator, 1);
      this.state.metadata.spectatorCount = this.state.spectatorList.length;
    }

    if (this.state.players.length === 1) {
      this.state.players[0].state = PlayerState.NotReady;
    }

    if (this.state.players.length === 0) {
      this.disconnect();
    }

    if (!this.state.isInBattle && this.roomName === "free_battle") {
      if (client.id === this.hostCLID) {
        console.log("Host left the room");
        this.disconnect();
      }
    } else if (this.state.isInBattle) {
      const playerId: string =
        this.state.players[0].sessionId == client.sessionId
          ? this.state.players[0].id
          : this.state.players[1].sessionId == client.sessionId
          ? this.state.players[1].id
          : "";
      this.broadcast("opponent_dis", playerId);
    }
    try {
      if (!consented) {
        await this.allowReconnection(client, 20);
      }
      client.leave();
    } catch (e) {
      client.leave();
    }
  }

  async onDispose() {
    this.presence.srem(this.LOBBY_CHANNEL, this.roomId);
  }

  convertRoomNameToMatchType(roomName: string): MatchType {
    const lowerCaseRoomName = roomName.toLowerCase();
    if (lowerCaseRoomName.includes("free")) {
      return MatchType.Free;
    } else if (lowerCaseRoomName.includes("ranked")) {
      return MatchType.Ranked;
    } else if (lowerCaseRoomName.includes("prized")) {
      return MatchType.Prized;
    } else if (lowerCaseRoomName.includes("tournament_match")) {
      return MatchType.TournamentMatch;
    } else if (lowerCaseRoomName.includes("tournament")) {
      return MatchType.Tournament;
    } else {
      throw new Error(`Unknown room name: ${roomName}`);
    }
  }

  getMonsterIds(playerId: string): string[] {
    const monsterIds: string[] = [];
    for (let monster of this.state.monsters) {
      if (monster.ownerId === playerId) {
        monsterIds.push(monster.monsterId);
      }
    }
    return monsterIds;
  }

  randomSpecialHex(): string {
    const rand = Math.random();
    if (rand < 0.33) return "Damage";
    else if (rand > 0.66) return "Unmovable";
    else return "None";
  }
}

class MoveMessage {
  monsterId: string;
  playerId: string;
  position: Position;
}

class PlaceMonster {
  playerId: string;
  monsterId: string;
  farmId: string;
  position: Position;
}
class Position {
  x: number;
  y: number;
}
enum MatchType {
  Free = 0,
  Ranked = 1,
  Prized = 2,
  Tournament = 3,
  TournamentMatch = 4,
}

class MonsterHit {
  monsterId: string;
  isHit: boolean;
  isCritical: boolean;
  skillDelayDamage: number;
  hpDamage: number;
  stDamage: number;

  constructor(
    monsterId: string,
    isHit: boolean,
    isCritical: boolean,
    skillDelayDamage: number,
    hpDamage: number,
    stDamage: number
  ) {
    this.monsterId = monsterId;
    this.isHit = isHit;
    this.isCritical = isCritical;
    this.skillDelayDamage = skillDelayDamage;
    this.hpDamage = hpDamage;
    this.stDamage = stDamage;
  }
}

class BuffMessage {
  monsterId: string;
  listBuff: string[] = [];
  constructor(monsterId: string, listBuff: string[] = []) {
    this.monsterId = monsterId;
    this.listBuff = listBuff;
  }
}
class DeBuffMessage {
  monsterId: string;
  listDeBuff: DeBuffHit[] = [];
  constructor(monsterId: string, listDeBuff: DeBuffHit[]) {
    this.monsterId = monsterId;
    this.listDeBuff = listDeBuff;
  }
}
class DeBuffHit {
  deBuffId: string;
  isHit: boolean;
  constructor(deBuffId: string, isHit: boolean) {
    this.deBuffId = deBuffId;
    this.isHit = isHit;
  }
}

class PlayerAction {
  unitAbility: string;
  playerId: string;
  position: Position;
  monsterHits: MonsterHit[] = [];
  buffMessages: BuffMessage[] = [];
  deBuffMessages: DeBuffMessage[] = [];
  skillDelay: number;
  skillStamina: number;
  isBlowSkill: boolean;
  isTrapSkill: boolean;
  monsterReviveId: string;
  isAI : boolean;


  constructor(
    unitAbility: string,
    playerId: string,
    position: Position,
    monsterHits: MonsterHit[],
    buffMessages: BuffMessage[],
    deBuffMessages: DeBuffMessage[],
    skillDelay: number,
    skillStamina: number,
    isBlowSkill: boolean,
    isTrapSkill: boolean,
    monsterReviveId: string,
    isAI:boolean
  ) {
    this.unitAbility = unitAbility;
    this.playerId = playerId;
    this.position = position;
    this.monsterHits = monsterHits;
    this.buffMessages = buffMessages;
    this.deBuffMessages = deBuffMessages;
    this.skillDelay = skillDelay;
    this.skillStamina = skillStamina;
    this.isBlowSkill = isBlowSkill;
    this.isTrapSkill = isTrapSkill;
    this.monsterReviveId = monsterReviveId;
    this.isAI = isAI;
  }
}
function getRandomFloat(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

import {Schema, type, ArraySchema, MapSchema} from "@colyseus/schema";
import {Monster} from "./Monster";
import {string} from "@colyseus/schema/lib/encoding/decode";

export enum PlayerState {
  NotReady = "NotReady",
  ReadyToStart = "ReadyToStart",
  ReadyInPartySetting = "ReadyInPartySetting",
  ReadyToPlaceMonster = "ReadyToPlaceMonster",
  ReadyToPlay = "ReadyToPlay",
  Disconnected = "Disconnected"
}

export class Player extends Schema {
  @type("string")
  id: string;

  @type("string")
  state: PlayerState;

  @type("string")
  sessionId: string;

  @type("string")
  walletId: string;

  constructor(id: string, sessionId: string, walletId: string) {
    super();
    this.id = id;
    this.state = PlayerState.NotReady;
    this.sessionId = sessionId;
    this.walletId = walletId;
  }
}

class Position {
  x: number;
  y: number;
}

export class UpdateData {
  playerId: string;
  monsterId: string;
  farmId: string;
  position: Position;
}


export class PlayerRoles extends Schema {
  @type("string") firstPlayerId: string = "";
  @type("string") secondPlayerId: string = "";
}

export class RoomMetadataSchema extends Schema {
  @type("string") password: string = "";
  @type("boolean") isPrivate: boolean = false;
  @type("string") firstPlayer: string = "";
  @type("string") secondPlayer: string = "";
  @type("boolean") audience: boolean = false;
  @type("boolean") opponent: boolean = false;
  @type("string") monsterRank: string = "F";
  @type("string") monsterNum: string = "1";
  @type("string") turnNum: string = "10";
  @type("string") specialHex: string = "None";
  @type("boolean") memoryAllow: boolean = false;
  @type("string") hostId: string = "";
  @type("int32") spectatorCount: number = 0;
}

//create arrayschema to store currentMonsterTurn
export class CurrentMonsterTurn extends Schema {
  @type("string") monsterId: string = "";
  @type("string") playerId: string = "";
}

//create arrayschema list to store nextMonsterTurn
export class NextMonsterTurn extends Schema {
  @type("string") monsterId: string = "";
  @type("string") playerId: string = "";
}


export class GameState extends Schema {
  @type("string") roomName: string = "";
  @type(RoomMetadataSchema) metadata: RoomMetadataSchema = new RoomMetadataSchema();
  @type([Player]) players: ArraySchema<Player> = new ArraySchema<Player>();
  @type(PlayerRoles) playerRoles: PlayerRoles = new PlayerRoles();
  @type("boolean") isInBattle: boolean = false;
  @type("number") countdown = 60;
  @type([Monster]) monsters: ArraySchema<Monster> = new ArraySchema<Monster>();
  @type("number") turnCount: number = 0;
  @type(["string"]) currentMonsterTurn: ArraySchema<string> = new ArraySchema<string>();
  @type(["string"]) nextMonsterTurn: ArraySchema<string> = new ArraySchema<string>();
  @type("string") currentSelectedMonster: string="";
  @type(["string"]) spectatorList: ArraySchema<string> = new ArraySchema<string>();
  @type("number") random: number = 0;

  addPlayer(playerId: string, sessionId: string, walletId: string) {
    this.players.push(new Player(playerId, sessionId,walletId));
  }

  addMonster(ownerId: string, farmId: string, randomNumberCorrection: number, jsonMonsterData: string, jsonFarmData: string = "") {
    const monster = new Monster();
    monster.randomNumberCorrection = randomNumberCorrection;
    monster.ownerId = ownerId;
    monster.farmId = farmId;
    monster.farmData = jsonFarmData;
    try{
      const parsedData = JSON.parse(jsonMonsterData);
      monster.monsterId = parsedData._id;
      monster.monsterData = jsonMonsterData;
    }
    catch(e){
      monster.monsterId = jsonMonsterData;
      monster.monsterData = jsonMonsterData;
    }

    this.monsters.push(monster);
  }

  setPlayerState(playerId: string, newState: PlayerState) {
    const player = this.players.find(p => p.id === playerId);
    if (player) {
      player.state = newState;
    }
  }

  allPlayersReady(requiredState: PlayerState): boolean {
    return this.players.every(p => p.state === requiredState);
  }

  // remove all monsters from state using splice
  removeAllMonsters() {
    this.monsters.splice(0, this.monsters.length);
  }
}


// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class Debuffs : Schema {
		[Type(0, "string")]
		public string debuffEnum = default(string);

		[Type(1, "float32")]
		public float value = default(float);

		[Type(2, "float32")]
		public float duration = default(float);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __debuffEnumChange;
		public Action OnDebuffEnumChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.debuffEnum));
			__debuffEnumChange += __handler;
			if (__immediate && this.debuffEnum != default(string)) { __handler(this.debuffEnum, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(debuffEnum));
				__debuffEnumChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __valueChange;
		public Action OnValueChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.value));
			__valueChange += __handler;
			if (__immediate && this.value != default(float)) { __handler(this.value, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(value));
				__valueChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __durationChange;
		public Action OnDurationChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.duration));
			__durationChange += __handler;
			if (__immediate && this.duration != default(float)) { __handler(this.duration, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(duration));
				__durationChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(debuffEnum): __debuffEnumChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(value): __valueChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(duration): __durationChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

namespace Network.RoomState {
	public struct PlayerState {

		public const string NotReady = "NotReady";
		public const string ReadyToStart = "ReadyToStart";
		public const string ReadyInPartySetting = "ReadyInPartySetting";
		public const string ReadyToPlaceMonster = "ReadyToPlaceMonster";
		public const string ReadyToPlay = "ReadyToPlay";
		public const string Disconnected = "Disconnected";
	}
}
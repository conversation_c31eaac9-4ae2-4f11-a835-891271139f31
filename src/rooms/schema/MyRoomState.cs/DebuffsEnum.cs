// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

namespace Network.RoomState {
	public struct DebuffsEnum {

		public const string Poison = "Poison";
		public const string Toxic = "Toxic";
		public const string Weakend = "Weakend";
		public const string Blind = "Blind";
		public const string Slow = "Slow";
		public const string Paralyze = "Paralyze";
		public const string Sleep = "Sleep";
		public const string StrDecrease = "StrDecrease";
		public const string IntDecrease = "IntDecrease";
		public const string DexDecrease = "DexDecrease";
		public const string AgiDecrease = "AgiDecrease";
		public const string VitDecrease = "VitDecrease";
		public const string Enrage = "Enrage";
		public const string Fear = "Fear";
		public const string Confusion = "Confusion";
		public const string Charm = "Charm";
	}
}
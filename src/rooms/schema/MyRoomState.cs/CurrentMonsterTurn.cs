// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class CurrentMonsterTurn : Schema {
		[Type(0, "string")]
		public string monsterId = default(string);

		[Type(1, "string")]
		public string playerId = default(string);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __monsterIdChange;
		public Action OnMonsterIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsterId));
			__monsterIdChange += __handler;
			if (__immediate && this.monsterId != default(string)) { __handler(this.monsterId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsterId));
				__monsterIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __playerIdChange;
		public Action OnPlayerIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.playerId));
			__playerIdChange += __handler;
			if (__immediate && this.playerId != default(string)) { __handler(this.playerId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(playerId));
				__playerIdChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(monsterId): __monsterIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(playerId): __playerIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

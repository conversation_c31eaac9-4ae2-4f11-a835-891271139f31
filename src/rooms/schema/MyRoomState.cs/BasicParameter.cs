// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class BasicParameter : Schema {
		[Type(0, "float32")]
		public float health = default(float);

		[Type(1, "float32")]
		public float strength = default(float);

		[Type(2, "float32")]
		public float intelligent = default(float);

		[Type(3, "float32")]
		public float dexterity = default(float);

		[Type(4, "float32")]
		public float agility = default(float);

		[Type(5, "float32")]
		public float vitality = default(float);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<float> __healthChange;
		public Action OnHealthChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.health));
			__healthChange += __handler;
			if (__immediate && this.health != default(float)) { __handler(this.health, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(health));
				__healthChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __strengthChange;
		public Action OnStrengthChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.strength));
			__strengthChange += __handler;
			if (__immediate && this.strength != default(float)) { __handler(this.strength, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(strength));
				__strengthChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __intelligentChange;
		public Action OnIntelligentChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.intelligent));
			__intelligentChange += __handler;
			if (__immediate && this.intelligent != default(float)) { __handler(this.intelligent, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(intelligent));
				__intelligentChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __dexterityChange;
		public Action OnDexterityChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.dexterity));
			__dexterityChange += __handler;
			if (__immediate && this.dexterity != default(float)) { __handler(this.dexterity, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(dexterity));
				__dexterityChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __agilityChange;
		public Action OnAgilityChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.agility));
			__agilityChange += __handler;
			if (__immediate && this.agility != default(float)) { __handler(this.agility, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(agility));
				__agilityChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __vitalityChange;
		public Action OnVitalityChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.vitality));
			__vitalityChange += __handler;
			if (__immediate && this.vitality != default(float)) { __handler(this.vitality, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(vitality));
				__vitalityChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(health): __healthChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(strength): __strengthChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(intelligent): __intelligentChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(dexterity): __dexterityChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(agility): __agilityChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(vitality): __vitalityChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class Monster : Schema {
		[Type(0, "string")]
		public string ownerId = default(string);

		[Type(1, "string")]
		public string monsterData = default(string);

		[Type(2, "string")]
		public string farmData = default(string);

		[Type(3, "string")]
		public string monsterId = default(string);

		[Type(4, "string")]
		public string farmId = default(string);

		[Type(5, "float32")]
		public float randomNumberCorrection = default(float);

		[Type(6, "float32")]
		public float currentSpeed = default(float);

		[Type(7, "float32")]
		public float skillDelay = default(float);

		[Type(8, "float32")]
		public float currentStaminaRecover = default(float);

		[Type(9, "float32")]
		public float currentMoveLimit = default(float);

		[Type(10, "float32")]
		public float currentHealthPercent = default(float);

		[Type(11, "boolean")]
		public bool isDead = default(bool);

		[Type(12, "ref", typeof(Vector2Cus))]
		public Vector2Cus position = new Vector2Cus();

		[Type(13, "ref", typeof(BasicParameter))]
		public BasicParameter basicParameter = new BasicParameter();

		[Type(14, "array", typeof(ArraySchema<Buffs>))]
		public ArraySchema<Buffs> buffs = new ArraySchema<Buffs>();

		[Type(15, "array", typeof(ArraySchema<Debuffs>))]
		public ArraySchema<Debuffs> debuffs = new ArraySchema<Debuffs>();

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __ownerIdChange;
		public Action OnOwnerIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.ownerId));
			__ownerIdChange += __handler;
			if (__immediate && this.ownerId != default(string)) { __handler(this.ownerId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(ownerId));
				__ownerIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __monsterDataChange;
		public Action OnMonsterDataChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsterData));
			__monsterDataChange += __handler;
			if (__immediate && this.monsterData != default(string)) { __handler(this.monsterData, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsterData));
				__monsterDataChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __farmDataChange;
		public Action OnFarmDataChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.farmData));
			__farmDataChange += __handler;
			if (__immediate && this.farmData != default(string)) { __handler(this.farmData, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(farmData));
				__farmDataChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __monsterIdChange;
		public Action OnMonsterIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsterId));
			__monsterIdChange += __handler;
			if (__immediate && this.monsterId != default(string)) { __handler(this.monsterId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsterId));
				__monsterIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __farmIdChange;
		public Action OnFarmIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.farmId));
			__farmIdChange += __handler;
			if (__immediate && this.farmId != default(string)) { __handler(this.farmId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(farmId));
				__farmIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __randomNumberCorrectionChange;
		public Action OnRandomNumberCorrectionChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.randomNumberCorrection));
			__randomNumberCorrectionChange += __handler;
			if (__immediate && this.randomNumberCorrection != default(float)) { __handler(this.randomNumberCorrection, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(randomNumberCorrection));
				__randomNumberCorrectionChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __currentSpeedChange;
		public Action OnCurrentSpeedChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentSpeed));
			__currentSpeedChange += __handler;
			if (__immediate && this.currentSpeed != default(float)) { __handler(this.currentSpeed, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentSpeed));
				__currentSpeedChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __skillDelayChange;
		public Action OnSkillDelayChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.skillDelay));
			__skillDelayChange += __handler;
			if (__immediate && this.skillDelay != default(float)) { __handler(this.skillDelay, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(skillDelay));
				__skillDelayChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __currentStaminaRecoverChange;
		public Action OnCurrentStaminaRecoverChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentStaminaRecover));
			__currentStaminaRecoverChange += __handler;
			if (__immediate && this.currentStaminaRecover != default(float)) { __handler(this.currentStaminaRecover, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentStaminaRecover));
				__currentStaminaRecoverChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __currentMoveLimitChange;
		public Action OnCurrentMoveLimitChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentMoveLimit));
			__currentMoveLimitChange += __handler;
			if (__immediate && this.currentMoveLimit != default(float)) { __handler(this.currentMoveLimit, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentMoveLimit));
				__currentMoveLimitChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __currentHealthPercentChange;
		public Action OnCurrentHealthPercentChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentHealthPercent));
			__currentHealthPercentChange += __handler;
			if (__immediate && this.currentHealthPercent != default(float)) { __handler(this.currentHealthPercent, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentHealthPercent));
				__currentHealthPercentChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __isDeadChange;
		public Action OnIsDeadChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.isDead));
			__isDeadChange += __handler;
			if (__immediate && this.isDead != default(bool)) { __handler(this.isDead, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(isDead));
				__isDeadChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<Vector2Cus> __positionChange;
		public Action OnPositionChange(PropertyChangeHandler<Vector2Cus> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.position));
			__positionChange += __handler;
			if (__immediate && this.position != null) { __handler(this.position, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(position));
				__positionChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<BasicParameter> __basicParameterChange;
		public Action OnBasicParameterChange(PropertyChangeHandler<BasicParameter> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.basicParameter));
			__basicParameterChange += __handler;
			if (__immediate && this.basicParameter != null) { __handler(this.basicParameter, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(basicParameter));
				__basicParameterChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<Buffs>> __buffsChange;
		public Action OnBuffsChange(PropertyChangeHandler<ArraySchema<Buffs>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.buffs));
			__buffsChange += __handler;
			if (__immediate && this.buffs != null) { __handler(this.buffs, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(buffs));
				__buffsChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<Debuffs>> __debuffsChange;
		public Action OnDebuffsChange(PropertyChangeHandler<ArraySchema<Debuffs>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.debuffs));
			__debuffsChange += __handler;
			if (__immediate && this.debuffs != null) { __handler(this.debuffs, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(debuffs));
				__debuffsChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(ownerId): __ownerIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(monsterData): __monsterDataChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(farmData): __farmDataChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(monsterId): __monsterIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(farmId): __farmIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(randomNumberCorrection): __randomNumberCorrectionChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(currentSpeed): __currentSpeedChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(skillDelay): __skillDelayChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(currentStaminaRecover): __currentStaminaRecoverChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(currentMoveLimit): __currentMoveLimitChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(currentHealthPercent): __currentHealthPercentChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(isDead): __isDeadChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(position): __positionChange?.Invoke((Vector2Cus) change.Value, (Vector2Cus) change.PreviousValue); break;
				case nameof(basicParameter): __basicParameterChange?.Invoke((BasicParameter) change.Value, (BasicParameter) change.PreviousValue); break;
				case nameof(buffs): __buffsChange?.Invoke((ArraySchema<Buffs>) change.Value, (ArraySchema<Buffs>) change.PreviousValue); break;
				case nameof(debuffs): __debuffsChange?.Invoke((ArraySchema<Debuffs>) change.Value, (ArraySchema<Debuffs>) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

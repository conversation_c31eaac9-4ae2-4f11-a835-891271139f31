// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class GameState : Schema {
		[Type(0, "string")]
		public string roomName = default(string);

		[Type(1, "ref", typeof(RoomMetadataSchema))]
		public RoomMetadataSchema metadata = new RoomMetadataSchema();

		[Type(2, "array", typeof(ArraySchema<Player>))]
		public ArraySchema<Player> players = new ArraySchema<Player>();

		[Type(3, "ref", typeof(PlayerRoles))]
		public PlayerRoles playerRoles = new PlayerRoles();

		[Type(4, "boolean")]
		public bool isInBattle = default(bool);

		[Type(5, "number")]
		public float countdown = default(float);

		[Type(6, "array", typeof(ArraySchema<Monster>))]
		public ArraySchema<Monster> monsters = new ArraySchema<Monster>();

		[Type(7, "number")]
		public float turnCount = default(float);

		[Type(8, "array", typeof(ArraySchema<string>), "string")]
		public ArraySchema<string> currentMonsterTurn = new ArraySchema<string>();

		[Type(9, "array", typeof(ArraySchema<string>), "string")]
		public ArraySchema<string> nextMonsterTurn = new ArraySchema<string>();

		[Type(10, "string")]
		public string currentSelectedMonster = default(string);

		[Type(11, "array", typeof(ArraySchema<string>), "string")]
		public ArraySchema<string> spectatorList = new ArraySchema<string>();

		[Type(12, "number")]
		public float random = default(float);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __roomNameChange;
		public Action OnRoomNameChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.roomName));
			__roomNameChange += __handler;
			if (__immediate && this.roomName != default(string)) { __handler(this.roomName, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(roomName));
				__roomNameChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<RoomMetadataSchema> __metadataChange;
		public Action OnMetadataChange(PropertyChangeHandler<RoomMetadataSchema> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.metadata));
			__metadataChange += __handler;
			if (__immediate && this.metadata != null) { __handler(this.metadata, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(metadata));
				__metadataChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<Player>> __playersChange;
		public Action OnPlayersChange(PropertyChangeHandler<ArraySchema<Player>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.players));
			__playersChange += __handler;
			if (__immediate && this.players != null) { __handler(this.players, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(players));
				__playersChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<PlayerRoles> __playerRolesChange;
		public Action OnPlayerRolesChange(PropertyChangeHandler<PlayerRoles> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.playerRoles));
			__playerRolesChange += __handler;
			if (__immediate && this.playerRoles != null) { __handler(this.playerRoles, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(playerRoles));
				__playerRolesChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __isInBattleChange;
		public Action OnIsInBattleChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.isInBattle));
			__isInBattleChange += __handler;
			if (__immediate && this.isInBattle != default(bool)) { __handler(this.isInBattle, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(isInBattle));
				__isInBattleChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __countdownChange;
		public Action OnCountdownChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.countdown));
			__countdownChange += __handler;
			if (__immediate && this.countdown != default(float)) { __handler(this.countdown, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(countdown));
				__countdownChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<Monster>> __monstersChange;
		public Action OnMonstersChange(PropertyChangeHandler<ArraySchema<Monster>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsters));
			__monstersChange += __handler;
			if (__immediate && this.monsters != null) { __handler(this.monsters, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsters));
				__monstersChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __turnCountChange;
		public Action OnTurnCountChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.turnCount));
			__turnCountChange += __handler;
			if (__immediate && this.turnCount != default(float)) { __handler(this.turnCount, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(turnCount));
				__turnCountChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<string>> __currentMonsterTurnChange;
		public Action OnCurrentMonsterTurnChange(PropertyChangeHandler<ArraySchema<string>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentMonsterTurn));
			__currentMonsterTurnChange += __handler;
			if (__immediate && this.currentMonsterTurn != null) { __handler(this.currentMonsterTurn, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentMonsterTurn));
				__currentMonsterTurnChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<string>> __nextMonsterTurnChange;
		public Action OnNextMonsterTurnChange(PropertyChangeHandler<ArraySchema<string>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.nextMonsterTurn));
			__nextMonsterTurnChange += __handler;
			if (__immediate && this.nextMonsterTurn != null) { __handler(this.nextMonsterTurn, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(nextMonsterTurn));
				__nextMonsterTurnChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __currentSelectedMonsterChange;
		public Action OnCurrentSelectedMonsterChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.currentSelectedMonster));
			__currentSelectedMonsterChange += __handler;
			if (__immediate && this.currentSelectedMonster != default(string)) { __handler(this.currentSelectedMonster, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(currentSelectedMonster));
				__currentSelectedMonsterChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<ArraySchema<string>> __spectatorListChange;
		public Action OnSpectatorListChange(PropertyChangeHandler<ArraySchema<string>> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.spectatorList));
			__spectatorListChange += __handler;
			if (__immediate && this.spectatorList != null) { __handler(this.spectatorList, null); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(spectatorList));
				__spectatorListChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<float> __randomChange;
		public Action OnRandomChange(PropertyChangeHandler<float> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.random));
			__randomChange += __handler;
			if (__immediate && this.random != default(float)) { __handler(this.random, default(float)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(random));
				__randomChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(roomName): __roomNameChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(metadata): __metadataChange?.Invoke((RoomMetadataSchema) change.Value, (RoomMetadataSchema) change.PreviousValue); break;
				case nameof(players): __playersChange?.Invoke((ArraySchema<Player>) change.Value, (ArraySchema<Player>) change.PreviousValue); break;
				case nameof(playerRoles): __playerRolesChange?.Invoke((PlayerRoles) change.Value, (PlayerRoles) change.PreviousValue); break;
				case nameof(isInBattle): __isInBattleChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(countdown): __countdownChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(monsters): __monstersChange?.Invoke((ArraySchema<Monster>) change.Value, (ArraySchema<Monster>) change.PreviousValue); break;
				case nameof(turnCount): __turnCountChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				case nameof(currentMonsterTurn): __currentMonsterTurnChange?.Invoke((ArraySchema<string>) change.Value, (ArraySchema<string>) change.PreviousValue); break;
				case nameof(nextMonsterTurn): __nextMonsterTurnChange?.Invoke((ArraySchema<string>) change.Value, (ArraySchema<string>) change.PreviousValue); break;
				case nameof(currentSelectedMonster): __currentSelectedMonsterChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(spectatorList): __spectatorListChange?.Invoke((ArraySchema<string>) change.Value, (ArraySchema<string>) change.PreviousValue); break;
				case nameof(random): __randomChange?.Invoke((float) change.Value, (float) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class PlayerRoles : Schema {
		[Type(0, "string")]
		public string firstPlayerId = default(string);

		[Type(1, "string")]
		public string secondPlayerId = default(string);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __firstPlayerIdChange;
		public Action OnFirstPlayerIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.firstPlayerId));
			__firstPlayerIdChange += __handler;
			if (__immediate && this.firstPlayerId != default(string)) { __handler(this.firstPlayerId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(firstPlayerId));
				__firstPlayerIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __secondPlayerIdChange;
		public Action OnSecondPlayerIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.secondPlayerId));
			__secondPlayerIdChange += __handler;
			if (__immediate && this.secondPlayerId != default(string)) { __handler(this.secondPlayerId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(secondPlayerId));
				__secondPlayerIdChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(firstPlayerId): __firstPlayerIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(secondPlayerId): __secondPlayerIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

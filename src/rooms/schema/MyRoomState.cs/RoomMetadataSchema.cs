// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

using Colyseus.Schema;
using Action = System.Action;

namespace Network.RoomState {
	public partial class RoomMetadataSchema : Schema {
		[Type(0, "string")]
		public string password = default(string);

		[Type(1, "boolean")]
		public bool isPrivate = default(bool);

		[Type(2, "string")]
		public string firstPlayer = default(string);

		[Type(3, "string")]
		public string secondPlayer = default(string);

		[Type(4, "boolean")]
		public bool audience = default(bool);

		[Type(5, "boolean")]
		public bool opponent = default(bool);

		[Type(6, "string")]
		public string monsterRank = default(string);

		[Type(7, "string")]
		public string monsterNum = default(string);

		[Type(8, "string")]
		public string turnNum = default(string);

		[Type(9, "string")]
		public string specialHex = default(string);

		[Type(10, "boolean")]
		public bool memoryAllow = default(bool);

		[Type(11, "string")]
		public string hostId = default(string);

		[Type(12, "int32")]
		public int spectatorCount = default(int);

		/*
		 * Support for individual property change callbacks below...
		 */

		protected event PropertyChangeHandler<string> __passwordChange;
		public Action OnPasswordChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.password));
			__passwordChange += __handler;
			if (__immediate && this.password != default(string)) { __handler(this.password, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(password));
				__passwordChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __isPrivateChange;
		public Action OnIsPrivateChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.isPrivate));
			__isPrivateChange += __handler;
			if (__immediate && this.isPrivate != default(bool)) { __handler(this.isPrivate, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(isPrivate));
				__isPrivateChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __firstPlayerChange;
		public Action OnFirstPlayerChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.firstPlayer));
			__firstPlayerChange += __handler;
			if (__immediate && this.firstPlayer != default(string)) { __handler(this.firstPlayer, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(firstPlayer));
				__firstPlayerChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __secondPlayerChange;
		public Action OnSecondPlayerChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.secondPlayer));
			__secondPlayerChange += __handler;
			if (__immediate && this.secondPlayer != default(string)) { __handler(this.secondPlayer, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(secondPlayer));
				__secondPlayerChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __audienceChange;
		public Action OnAudienceChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.audience));
			__audienceChange += __handler;
			if (__immediate && this.audience != default(bool)) { __handler(this.audience, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(audience));
				__audienceChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __opponentChange;
		public Action OnOpponentChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.opponent));
			__opponentChange += __handler;
			if (__immediate && this.opponent != default(bool)) { __handler(this.opponent, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(opponent));
				__opponentChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __monsterRankChange;
		public Action OnMonsterRankChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsterRank));
			__monsterRankChange += __handler;
			if (__immediate && this.monsterRank != default(string)) { __handler(this.monsterRank, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsterRank));
				__monsterRankChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __monsterNumChange;
		public Action OnMonsterNumChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.monsterNum));
			__monsterNumChange += __handler;
			if (__immediate && this.monsterNum != default(string)) { __handler(this.monsterNum, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(monsterNum));
				__monsterNumChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __turnNumChange;
		public Action OnTurnNumChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.turnNum));
			__turnNumChange += __handler;
			if (__immediate && this.turnNum != default(string)) { __handler(this.turnNum, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(turnNum));
				__turnNumChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __specialHexChange;
		public Action OnSpecialHexChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.specialHex));
			__specialHexChange += __handler;
			if (__immediate && this.specialHex != default(string)) { __handler(this.specialHex, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(specialHex));
				__specialHexChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<bool> __memoryAllowChange;
		public Action OnMemoryAllowChange(PropertyChangeHandler<bool> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.memoryAllow));
			__memoryAllowChange += __handler;
			if (__immediate && this.memoryAllow != default(bool)) { __handler(this.memoryAllow, default(bool)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(memoryAllow));
				__memoryAllowChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<string> __hostIdChange;
		public Action OnHostIdChange(PropertyChangeHandler<string> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.hostId));
			__hostIdChange += __handler;
			if (__immediate && this.hostId != default(string)) { __handler(this.hostId, default(string)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(hostId));
				__hostIdChange -= __handler;
			};
		}

		protected event PropertyChangeHandler<int> __spectatorCountChange;
		public Action OnSpectatorCountChange(PropertyChangeHandler<int> __handler, bool __immediate = true) {
			if (__callbacks == null) { __callbacks = new SchemaCallbacks(); }
			__callbacks.AddPropertyCallback(nameof(this.spectatorCount));
			__spectatorCountChange += __handler;
			if (__immediate && this.spectatorCount != default(int)) { __handler(this.spectatorCount, default(int)); }
			return () => {
				__callbacks.RemovePropertyCallback(nameof(spectatorCount));
				__spectatorCountChange -= __handler;
			};
		}

		protected override void TriggerFieldChange(DataChange change) {
			switch (change.Field) {
				case nameof(password): __passwordChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(isPrivate): __isPrivateChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(firstPlayer): __firstPlayerChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(secondPlayer): __secondPlayerChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(audience): __audienceChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(opponent): __opponentChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(monsterRank): __monsterRankChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(monsterNum): __monsterNumChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(turnNum): __turnNumChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(specialHex): __specialHexChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(memoryAllow): __memoryAllowChange?.Invoke((bool) change.Value, (bool) change.PreviousValue); break;
				case nameof(hostId): __hostIdChange?.Invoke((string) change.Value, (string) change.PreviousValue); break;
				case nameof(spectatorCount): __spectatorCountChange?.Invoke((int) change.Value, (int) change.PreviousValue); break;
				default: break;
			}
		}
	}
}

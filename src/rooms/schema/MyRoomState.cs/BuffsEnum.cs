// 
// THIS FILE HAS BEEN GENERATED AUTOMATICALLY
// DO NOT CHANGE IT MANUALLY UNLESS YOU KNOW WHAT YOU'RE DOING
// 
// GENERATED USING @colyseus/schema 2.0.34
// 

namespace Network.RoomState {
	public struct BuffsEnum {

		public const string Veil = "Veil";
		public const string Barrier = "Barrier";
		public const string Activation = "Activation";
		public const string Acceleration = "Acceleration";
		public const string Sink = "Sink";
		public const string Illusion = "Illusion";
		public const string Provocation = "Provocation";
		public const string Invisibility = "Invisibility";
		public const string StrIncrease = "StrIncrease";
		public const string IntIncrease = "IntIncrease";
		public const string DexIncrease = "DexIncrease";
		public const string AgiIncrease = "AgiIncrease";
		public const string VitIncrease = "VitIncrease";
		public const string Courage = "Courage";
		public const string Cheering = "Cheering";
		public const string Concentration = "Concentration";
		public const string Energize = "Energize";
	}
}
import * as https from "https";

export enum TerrainsTypeEnum {
  None = "None",
  Plain = "Plain",
  Forest = "Forest",
  Waterfront = "Waterfront",
  Desert = "Desert",
  Wasteland = "Wasteland",
  Mountain = "Mountain",
  Volcano = "Volcano"
}

export class SerializeBasicParameterSaving {
  health: number;
  strength: number;
  intelligent: number;
  dexterity: number;
  agility: number;
  vitality: number;

  constructor(
    health: number = 0,
    strength: number = 0,
    intelligent: number = 0,
    dexterity: number = 0,
    agility: number = 0,
    vitality: number = 0
  ) {
    this.health = health;
    this.strength = strength;
    this.intelligent = intelligent;
    this.dexterity = dexterity;
    this.agility = agility;
    this.vitality = vitality;
  }
}

export class SerializePlayerFarm {
  _id: string;
  farm_nft_id: number;
  farm_name: string;
  farm_trait_id: string;
  farm_terrain: TerrainsTypeEnum;
  farm_basic_enhance_value: SerializeBasicParameterSaving;
  farm_max_enhance_value: SerializeBasicParameterSaving;
  special_training_value: SerializeBasicParameterSaving;
  farm_stress_enhance_value: number;
  farm_fatigue_enhance_value: number;
  farm_rest_enhance_value: number;
  farm_max_stress_enhance_value: number;
  farm_max_fatigue_enhance_value: number;
  farm_max_rest_enhance_value: number;
  is_farm_favorite: boolean;
  is_free_farm: boolean;

  constructor() {
    this._id = "";
    this.farm_nft_id = 0;
    this.farm_name = "";
    this.farm_trait_id = "";
    this.farm_terrain = TerrainsTypeEnum.None;
    this.farm_basic_enhance_value = new SerializeBasicParameterSaving();
    this.farm_max_enhance_value = new SerializeBasicParameterSaving();
    this.special_training_value = new SerializeBasicParameterSaving();
    this.farm_stress_enhance_value = 0;
    this.farm_fatigue_enhance_value = 0;
    this.farm_rest_enhance_value = 0;
    this.farm_max_stress_enhance_value = 0;
    this.farm_max_fatigue_enhance_value = 0;
    this.farm_max_rest_enhance_value = 0;
    this.is_farm_favorite = false;
    this.is_free_farm = false;
  }
}

export interface PlayerFarm {
  _id: string;
  crt_dt: string;
  crt_by: string;
  ipaddress: string;
  player_id: string;
  farm_nft_id: number;
  farm_name: string;
  farm_trait_id: string;
  farm_terrain: TerrainsTypeEnum;
  farm_basic_enhance_value: {
    health: number;
    strength: number;
    intelligent: number;
    dexterity: number;
    agility: number;
    vitality: number;
  };
  farm_max_enhance_value: {
    health: number;
    strength: number;
    intelligent: number;
    dexterity: number;
    agility: number;
    vitality: number;
  };
  special_training_value: {
    health: number;
    strength: number;
    intelligent: number;
    dexterity: number;
    agility: number;
    vitality: number;
  };
  farm_stress_enhance_value: number;
  farm_fatigue_enhance_value: number;
  farm_rest_enhance_value: number;
  farm_max_stress_enhance_value: number;
  farm_max_fatigue_enhance_value: number;
  farm_max_rest_enhance_value: number;
  is_farm_favorite: boolean;
  is_free_farm: boolean;
  mod_dt: string;
  mod_by: string;
  mod_ipaddress: string;
}

export interface PlayerFarmData {
  player_farm_list: PlayerFarm[];
}

export interface PlayerFarmResponse {
  success: boolean;
  message: string;
  data: PlayerFarmData;
  code: number;
}

export const GetFarmData = async (token: string): Promise<PlayerFarmResponse> => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: process.env.API_URL,
      port: 443,
      path: "/api/game/farm/balance",
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const req = https.request(options, (res) => {
      let data = "";

      // Collect response chunks
      res.on("data", (chunk) => {
        data += chunk;
      });

      // End of response
      res.on("end", () => {
        try {
          const parsedData = JSON.parse(data);
          resolve(parsedData);
        } catch (error) {
          reject({ success: false, error: "Error parsing response" });
        }
      });
    });

    // Handle request error
    req.on("error", (e) => {
      reject({ success: false, error: e.message });
    });

    // End the request
    req.end();
  });
};

import * as https from 'https';

export interface RankedMatchHistory {
    match_time: number;
    user_wallet_a: string;
    user_wallet_a_overall: number;
    user_a_monsters: string[];
    user_a_score: number;
    user_a_streak: number;
    user_wallet_b: string;
    user_wallet_b_overall: number;
    user_b_monsters: string[];
    user_b_score: number;
    user_b_streak: number;
    battle_type: number;
    battle_rank: string;
    winner_user: string;
}

interface RequestBody {
    ranked_match_history: RankedMatchHistory;
}

export const postBattleData = (token: string, battleData: RankedMatchHistory): Promise<any> => {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: process.env.API_URL,
            port: 443,
            path: "/api/game/ranked/create",
            method: "POST",
            headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
            },
        };

        const req = https.request(options, (res) => {
            let data = "";

            res.on("data", (chunk) => {
                data += chunk;
            });

            res.on("end", () => {
                try {
                    const parsedData = JSON.parse(data);
                    resolve({ success: true, data: parsedData });
                } catch (error) {
                    reject({ success: false, error: "Error parsing response" });
                }
            });
        });

        req.on("error", (e) => {
            reject({ success: false, error: e.message });
        });

        // Prepare the request body in the specified format
        const jsonData: RequestBody = {
            ranked_match_history: {
                match_time: Math.floor(Date.now() / 1000), // Current Unix timestamp
                user_wallet_a: battleData.user_wallet_a,
                user_wallet_a_overall: battleData.user_wallet_a_overall,
                user_a_monsters: battleData.user_a_monsters,
                user_a_score: battleData.user_a_score,
                user_a_streak: battleData.user_a_streak,
                user_wallet_b: battleData.user_wallet_b,
                user_wallet_b_overall: battleData.user_wallet_b_overall,
                user_b_monsters: battleData.user_b_monsters,
                user_b_score: battleData.user_b_score,
                user_b_streak: battleData.user_b_streak,
                battle_type: battleData.battle_type, // Note the typo in "batte_type"
                battle_rank: battleData.battle_rank,
                winner_user: battleData.winner_user,
            },
        };

        // Send the JSON data as a string
        req.write(JSON.stringify(jsonData));
        req.end();
    });
};

import * as https from "https";

interface PlayerMonster {
  _id: string;
  crt_dt: string;
  crt_by: string;
  ipaddress: string;
  player_id: string;
  monster_nft_id: number;
  monster_name: string;
  mint_type: number;
  minter: string;
  nft_image_url: string;
  main_seed: string;
  sub_seed: string;
  cycle: string;
  born_of_1_token_id: number;
  born_of_1_address: string;
  born_of_1_id: number;
  born_of_1_chain: string;
  born_of_2_token_id: number;
  born_of_2_address: string;
  born_of_2_id: number;
  born_of_2_chain: string;
  main_seeds_specialty_terrain: string;
  main_seeds_poor_terrain: string;
  sub_seeds_specialty_terrain: string;
  sub_seeds_poor_terrain: string;
  hp_growth_potential: string;
  str_growth_potential: string;
  int_growth_potential: string;
  dex_growth_potential: string;
  agi_growth_potential: string;
  vit_growth_potential: string;
  growth_type: string;
  st: number;
  spd: number;
  mov: number;
  life_span: number;
  monster_age: number;
  battle_count: number;
  is_free_monster: boolean;
  is_crystalized: boolean;
  is_crystal_used: boolean;
  is_coached: boolean;
  is_favorite: boolean;
  is_rank_s_offical_clear: boolean;
  is_monster_grand_prix_clear: boolean;
  is_monster_crown_cup_clear: boolean;
  is_santuary_cup_clear: boolean;
  is_winner_cup_clear: boolean;
  is_re_master_cup_clear: boolean;
  overall: number;
  likes: string;
  dislikes: string;
  monster_script_id: string;
  monster_rank: number;
  monster_crystal_id: string;
  monster_personality_id: string;
  alter_basic_p: {
    health: number;
    strength: number;
    intelligent: number;
    dexterity: number;
    agility: number;
    vitality: number;
  };
  alter_training_p: {
    fatigue: number;
    stress: number;
    physical: number;
    training_policy: number;
    friendship: number;
    energy: number;
    body: number;
    condition: number;
    lifespan: number;
  };
  innate_trait_id_list: Array<{
    innate_trait_id: string;
  }>;
  skill_detail: Array<{
    skill_id: string;
    skill_level: number;
    is_skill_selected: boolean;
    skill_range: number;
    skill_scope_type: string;
    skill_scope_range: number;
    skill_element: string;
    skill_name: string;
    selected_level: number;
  }>;
  monster_acquired_traits: Array<{
    trait_id: string;
    trait_level: number;
  }>;
  monster_disease: number;
  monster_injury: number;
  monster_last_action: {
    training_action: number;
    basic_parameter_changes: {
      health: number;
      strength: number;
      intelligent: number;
      dexterity: number;
      agility: number;
      vitality: number;
    };
    growth_parameter_changes: {
      fatigue: number;
      stress: number;
      physical: number;
      training_policy: number;
      friendship: number;
      energy: number;
      body: number;
      condition: number;
      lifespan: number;
    };
    injury: number;
    diseases: number;
    basic_training_action: number;
    training_action_result: number;
    lost_weeks: number;
    spended_weeks: number;
    last_food_use: string;
    last_training_area: string;
  };
  rank_battle_count: number;
  is_life_potion_used: boolean;
  is_item_used: boolean;
  is_memory: boolean;
  is_alive: boolean;
  lifespan_fusion: number;
  lifespan: number;
  crystal_rank_use: number;
  crystal_parameter_use: number;
  mod_dt: string;
  mod_by: string;
  mod_ipaddress: string;
}

interface PlayerMonstersData {
  player_monster_list: PlayerMonster[];
}

interface PlayerMonstersResponse {
  success: boolean;
  message: string;
  data: PlayerMonstersData;
  code: number;
}

export const GetMonsterData = async (token: string): Promise<PlayerMonstersResponse> => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: process.env.API_URL,
      port: 443,
      path: "/api/game/monster/balance",
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const req = https.request(options, (res) => {
      let data = "";

      // Collect response chunks
      res.on("data", (chunk) => {
        data += chunk;
      });

      // End of response
      res.on("end", () => {
        try {
          const parsedData = JSON.parse(data);
          resolve(parsedData);
        } catch (error) {
          reject({ success: false, error: "Error parsing response" });
        }
      });
    });

    // Handle request error
    req.on("error", (e) => {
      reject({ success: false, error: e.message });
    });

    // End the request
    req.end();
  });
};

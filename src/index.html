<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs</title>
</head>
<body>
    <h1>Logs</h1>
    <pre id="logs"></pre>

    <script>
        async function fetchLogs() {
            const response = await fetch('/logs');
            const logs = await response.json();
            document.getElementById('logs').textContent = logs.join('\n');
        }

        fetchLogs();
        setInterval(fetchLogs, 5000); // Refresh logs every 5 seconds
    </script>
</body>
</html>